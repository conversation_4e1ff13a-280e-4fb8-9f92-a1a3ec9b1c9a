import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';
import { ChevronLeft, BarChart3 } from 'lucide-react';
import RequestDetailsHeader from '@/components/request-details/RequestDetailsHeader';
import RequestDetailsSummary from '@/components/request-details/RequestDetailsSummary';
import RequestResearchSummary from '@/components/request-details/RequestResearchSummary';
import RequestTabbedSection from '@/components/request-details/RequestTabbedSection';
import IndexEntriesList from '@/components/request-details/IndexEntriesList';
import RequestChatInterface from '@/components/request-details/RequestChatInterface';
import LoadingSpinner from '@/components/request-details/LoadingSpinner';
import NotFoundState from '@/components/request-details/NotFoundState';
import FloatingChat from '@/components/analysis-dashboard/FloatingChat';
import IndexDetailModal from '@/components/request-details/IndexDetailModal';
import ActeDetailModal from '@/components/request-details/ActeDetailModal';
import RequestDetailsSidebar from '@/components/request-details/RequestDetailsSidebar';
import RequestDetailsOverview from '@/components/request-details/RequestDetailsOverview';
import RequestDocumentsSection from '@/components/request-details/RequestDocumentsSection';
import RequestRelationsSection from '@/components/request-details/RequestRelationsSection';
import { useMermaidGraph } from '@/hooks/use-mermaid-graph';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const RequestDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();
  const [request, setRequest] = useState<any | null>(null);
  const [indexEntries, setIndexEntries] = useState<any[]>([]);
  const [actesByIndex, setActesByIndex] = useState<{ [key: string]: any[] }>({});
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [showSidebar, setShowSidebar] = useState<boolean>(false);
  const [selectedSection, setSelectedSection] = useState<string>('overview');
  const [selectedPdfUrl, setSelectedPdfUrl] = useState<string | null>(null);

  // Modal states for Mermaid node clicks
  const [selectedIndex, setSelectedIndex] = useState<any | null>(null);
  const [selectedActe, setSelectedActe] = useState<any | null>(null);
  const [isIndexModalOpen, setIsIndexModalOpen] = useState<boolean>(false);
  const [isActeModalOpen, setIsActeModalOpen] = useState<boolean>(false);

  // Use the Mermaid graph hook
  const { mermaidDefinition, handleMermaidNodeClick } = useMermaidGraph({
    request,
    indexEntries,
    actesByIndex
  });

  // Handle Mermaid node clicks with modal opening
  const onMermaidNodeClick = (nodeId: string, nodeType: 'request' | 'index' | 'acte' | 'radiated') => {
    const result = handleMermaidNodeClick(nodeId, nodeType);
    if (result?.type === 'index' && result.data) {
      setSelectedIndex(result.data);
      setIsIndexModalOpen(true);
    } else if (result?.type === 'acte' && result.data) {
      setSelectedActe(result.data);
      setIsActeModalOpen(true);
    }
  };

  useEffect(() => {
    const fetchRequestDetails = async () => {
      setIsLoading(true);

      try {
        // Fetch the request
        const { data: requestData, error: requestError } = await supabase
          .from('requests')
          .select('*')
          .eq('id', id)
          .single();

        if (requestError) throw requestError;

        setRequest(requestData);

        // Fetch index entries
        const { data: indexData, error: indexError } = await supabase
          .from('index')
          .select('*')
          .eq('request_id', id)
          .order('created_at', { ascending: false });

        if (indexError) throw indexError;

        // Sort index entries by doc_number if it exists
        const sortedIndexData = [...indexData].sort((a, b) => {
          // Handle missing values or NaN
          if (!a.doc_number && !b.doc_number) return 0;
          if (!a.doc_number) return 1;
          if (!b.doc_number) return -1;

          return Number(a.doc_number) - Number(b.doc_number);
        });

        setIndexEntries(sortedIndexData);

        // Fetch actes for the request
        const { data: actesData, error: actesError } = await supabase
          .from('actes')
          .select('*')
          .eq('request_id', id)
          .order('created_at', { ascending: false });

        if (actesError) throw actesError;

        // Group actes by index_id
        const actesMap: { [key: string]: any[] } = {};

        if (actesData) {
          sortedIndexData.forEach(index => {
            actesMap[index.id] = actesData.filter(acte => acte.index_id === index.id) || [];
          });
        }

        setActesByIndex(actesMap);

      } catch (error) {
        console.error('Error fetching request details:', error);
        toast({
          title: 'Erreur',
          description: 'Impossible de charger les détails de la demande. Veuillez réessayer plus tard.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchRequestDetails();
    }
  }, [id, toast]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!request) {
    return <NotFoundState />;
  }

  const analysisData = {
    request,
    indexes: indexEntries,
    actes: Object.values(actesByIndex).flat()
  };

  const renderSidebarContent = () => {
    switch (selectedSection) {
      case 'research-summary':
        return id ? <RequestResearchSummary requestId={id} /> : null;
      case 'highlights':
        return id ? <RequestTabbedSection requestId={id} /> : null;
      case 'research-steps':
        return <IndexEntriesList indexEntries={indexEntries} actesByIndex={actesByIndex} />;
      case 'documents':
        return (
          <RequestDocumentsSection
            indexEntries={indexEntries}
            actesByIndex={actesByIndex}
            selectedPdfUrl={selectedPdfUrl}
            onPdfSelect={setSelectedPdfUrl}
          />
        );
      case 'relations':
        return (
          <RequestRelationsSection
            mermaidDefinition={mermaidDefinition}
            onNodeClick={onMermaidNodeClick}
          />
        );
      default:
        return (
          <RequestDetailsOverview
            request={request}
            indexEntries={indexEntries}
            actesByIndex={actesByIndex}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Header Navigation */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Link
              to="/"
              className="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              Tableau de bord
            </Link>
            <span className="text-muted-foreground">•</span>
            <span className="text-sm font-medium">Détails de la demande</span>
          </div>

          <Button
            variant={showSidebar ? "default" : "outline"}
            onClick={() => setShowSidebar(!showSidebar)}
            className="flex items-center space-x-2"
          >
            <BarChart3 className="h-4 w-4" />
            <span>{showSidebar ? 'Vue classique' : 'Vue analytique'}</span>
          </Button>
        </div>

        {showSidebar ? (
          /* Sidebar Layout */
          <div className="flex gap-6">
            {/* Sidebar - Fixed/Sticky */}
            <RequestDetailsSidebar
              selectedSection={selectedSection}
              onSectionChange={setSelectedSection}
              request={request}
              indexEntries={indexEntries}
              actesByIndex={actesByIndex}
              selectedPdfUrl={selectedPdfUrl}
              onPdfSelect={setSelectedPdfUrl}
            />

            {/* Main Content */}
            <div className="flex-1">
              <Card>
                <CardContent className="p-6">
                  {renderSidebarContent()}
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          /* Original Classic Layout */
          <div className="space-y-6">
            <RequestDetailsHeader request={request} />
            <RequestDetailsSummary
              request={request}
              indexCount={indexEntries.length}
              acteCount={Object.values(actesByIndex).flat().length}
            />
            {id && <RequestResearchSummary requestId={id} />}
            {id && <RequestTabbedSection requestId={id} />}
            <IndexEntriesList indexEntries={indexEntries} actesByIndex={actesByIndex} />
            {id && (
              <Card>
                <CardContent className="p-6">
                  <RequestChatInterface requestId={id} />
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>

      {/* Floating Chat - Always present */}
      {id && (
        <FloatingChat
          requestId={id}
          currentSection={selectedSection}
          contextData={analysisData}
        />
      )}

      {/* Modals for Mermaid node clicks */}
      {selectedIndex && (
        <IndexDetailModal
          index={selectedIndex}
          open={isIndexModalOpen}
          onOpenChange={setIsIndexModalOpen}
        />
      )}

      {selectedActe && (
        <ActeDetailModal
          acte={selectedActe}
          open={isActeModalOpen}
          onOpenChange={setIsActeModalOpen}
        />
      )}
    </div>
  );
};

export default RequestDetails;
