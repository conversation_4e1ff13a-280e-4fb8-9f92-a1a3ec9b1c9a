import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Worker } from '@react-pdf-viewer/core';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import { Loader2, AlertCircle, Download, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { processGoogleDriveUrl, getGoogleDriveDownloadUrl } from '@/lib/document-utils';

import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';

interface PdfViewerProps {
  fileUrl: string;
  fileName?: string;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ fileUrl, fileName = 'document.pdf' }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processedUrl, setProcessedUrl] = useState<string>('');
  const [useIframe, setUseIframe] = useState(false);
  const { toast } = useToast();

  const defaultLayoutPluginInstance = defaultLayoutPlugin({
    sidebarTabs: (defaultTabs) => [
      defaultTabs[0], // Thumbnails
      defaultTabs[1], // Bookmarks
    ],
  });



  useEffect(() => {
    if (fileUrl) {
      setIsLoading(true);
      setError(null);

      // Check if it's a Google Drive URL
      const isGoogleDrive = fileUrl.includes('drive.google.com');

      if (isGoogleDrive) {
        // For Google Drive, use iframe approach
        const processed = processGoogleDriveUrl(fileUrl);
        setProcessedUrl(processed);
        setUseIframe(true);
        setIsLoading(false);
      } else {
        // For other URLs, try react-pdf-viewer
        setProcessedUrl(fileUrl);
        setUseIframe(false);
        setIsLoading(false);
      }
    }
  }, [fileUrl]);

  const handleDownload = async () => {
    try {
      const downloadUrl = getGoogleDriveDownloadUrl(fileUrl);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: 'Téléchargement démarré',
        description: `Le téléchargement de ${fileName} a commencé.`,
      });
    } catch (error) {
      toast({
        title: 'Erreur de téléchargement',
        description: 'Impossible de télécharger le fichier. Veuillez réessayer.',
        variant: 'destructive',
      });
    }
  };

  if (!fileUrl) {
    return (
      <div className="h-full w-full flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed">
        <div className="text-center">
          <AlertCircle className="h-6 w-6 text-gray-400 mx-auto mb-2" />
          <p className="text-xs text-gray-500 dark:text-gray-400">Aucun document sélectionné</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="h-full w-full flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg border">
        <div className="text-center">
          <Loader2 className="h-6 w-6 animate-spin text-primary mx-auto mb-2" />
          <p className="text-xs text-gray-600 dark:text-gray-400">Chargement...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full w-full flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg border">
        <div className="text-center">
          <AlertCircle className="h-6 w-6 text-red-400 mx-auto mb-2" />
          <p className="text-xs text-red-600 dark:text-red-400 mb-2">{error}</p>
          <Button variant="outline" size="sm" onClick={() => window.open(fileUrl, '_blank')}>
            <span className="text-xs">Ouvrir dans un nouvel onglet</span>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full border rounded-lg overflow-hidden bg-white dark:bg-gray-900">
      {/* Compact Toolbar */}
      <div className="flex items-center justify-between p-2 border-b bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center space-x-1">
          <span className="text-xs font-medium text-gray-700 dark:text-gray-300 truncate">
            {fileName}
          </span>
        </div>
        <div className="flex items-center space-x-1">
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={handleDownload}>
            <Download className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0" onClick={() => window.open(fileUrl, '_blank')}>
            <Eye className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* PDF Viewer */}
      <div className="h-[calc(100%-40px)]">
        {useIframe ? (
          <iframe
            src={processedUrl}
            className="w-full h-full border-0"
            title={fileName}
            onLoad={() => setIsLoading(false)}
            onError={() => {
              setError('Impossible de charger le document. Essayez de l\'ouvrir dans un nouvel onglet.');
              setIsLoading(false);
            }}
          />
        ) : (
          <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js">
            <Viewer
              fileUrl={processedUrl}
              plugins={[defaultLayoutPluginInstance]}
              onDocumentLoad={() => setIsLoading(false)}
            />
          </Worker>
        )}
      </div>
    </div>
  );
};

export default PdfViewer;
