import React, { useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';
import { ZoomIn, ZoomOut, RefreshCw, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface MermaidGraphProps {
  definition: string;
  onNodeClick?: (nodeId: string, nodeType: 'request' | 'index' | 'acte' | 'radiated') => void;
}

const MermaidGraph: React.FC<MermaidGraphProps> = ({ definition, onNodeClick }) => {
  const diagramRef = useRef<HTMLDivElement>(null);
  const [zoomLevel, setZoomLevel] = useState(100); // Percentage
  const [renderedSvg, setRenderedSvg] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize Mermaid with proper configuration
  const initializeMermaid = () => {
    if (!isInitialized) {
      // Check if dark mode is active
      const isDarkMode = document.documentElement.classList.contains('dark');

      mermaid.initialize({
        startOnLoad: false,
        theme: isDarkMode ? 'dark' : 'base',
        themeVariables: {
          primaryColor: isDarkMode ? '#3b82f6' : '#2563eb',
          primaryTextColor: isDarkMode ? '#ffffff' : '#000000',
          primaryBorderColor: isDarkMode ? '#1e40af' : '#1d4ed8',
          lineColor: isDarkMode ? '#6b7280' : '#374151',
          secondaryColor: isDarkMode ? '#1f2937' : '#f3f4f6',
          tertiaryColor: isDarkMode ? '#374151' : '#e5e7eb',
          background: isDarkMode ? 'transparent' : 'transparent',
          mainBkg: isDarkMode ? 'transparent' : 'transparent',
          secondBkg: isDarkMode ? '#374151' : '#f9fafb',
          tertiaryBkg: isDarkMode ? '#4b5563' : '#f3f4f6',
          edgeLabelBackground: isDarkMode ? '#1f2937' : '#ffffff',
          clusterBkg: isDarkMode ? '#1f2937' : '#f8fafc',
          clusterBorder: isDarkMode ? '#374151' : '#e2e8f0',
        },
        flowchart: {
          useMaxWidth: true,
          htmlLabels: true,
          curve: 'basis',
          padding: 30,
          nodeSpacing: 60,
          rankSpacing: 100,
          diagramPadding: 30,
        },
        securityLevel: 'loose', // Allow HTML in labels
      });
      setIsInitialized(true);
    }
  };

  const renderMermaid = () => {
    if (diagramRef.current) {
      diagramRef.current.innerHTML = '';
      setRenderedSvg(null); // Clear previous SVG

      // Initialize Mermaid first
      initializeMermaid();

      try {
        mermaid.render('mermaid-svg', definition)
          .then(({ svg }) => {
            if (diagramRef.current) {
              setRenderedSvg(svg);
            }
          })
          .catch(error => {
            console.error('Mermaid rendering error:', error);
            if (diagramRef.current) {
              diagramRef.current.innerHTML = `<p class="text-red-500">Error rendering graph: ${error.message}</p>`;
            }
          });
      } catch (e: any) {
        console.error('Mermaid parsing error:', e);
        if (diagramRef.current) {
          diagramRef.current.innerHTML = `<p class="text-red-500">Error parsing graph definition: ${e.message}</p>`;
        }
      }
    }
  };

  useEffect(() => {
    renderMermaid();
  }, [definition]);

  // Re-render when theme changes
  useEffect(() => {
    const handleThemeChange = () => {
      setIsInitialized(false); // Force re-initialization with new theme
      renderMermaid();
    };

    // Listen for theme changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          handleThemeChange();
        }
      });
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (diagramRef.current && renderedSvg) {
      diagramRef.current.innerHTML = renderedSvg;
      const svgElement = diagramRef.current.querySelector('svg');
      if (svgElement) {
        svgElement.style.transform = `scale(${zoomLevel / 100})`;
        svgElement.style.transformOrigin = 'center center';

        // Add click handlers to nodes if onNodeClick is provided
        if (onNodeClick) {
          const nodes = svgElement.querySelectorAll('.node');
          nodes.forEach((node) => {
            const nodeElement = node as HTMLElement;
            nodeElement.style.cursor = 'pointer';

            nodeElement.addEventListener('click', (e) => {
              e.preventDefault();
              e.stopPropagation();

              // Extract node ID from the node element
              const nodeId = nodeElement.id || '';
              let nodeType: 'request' | 'index' | 'acte' | 'radiated' = 'request';

              // Determine node type based on ID pattern
              if (nodeId.startsWith('flowchart-Request-')) {
                nodeType = 'request';
              } else if (nodeId.startsWith('flowchart-I')) {
                nodeType = 'index';
              } else if (nodeId.startsWith('flowchart-A')) {
                nodeType = 'acte';
              } else if (nodeId.startsWith('flowchart-Rad')) {
                nodeType = 'radiated';
              }

              // Extract the actual ID (remove flowchart prefix)
              const actualId = nodeId.replace(/^flowchart-/, '').replace(/-\d+$/, '');

              onNodeClick(actualId, nodeType);
            });
          });
        }
      }
    }
  }, [zoomLevel, renderedSvg, onNodeClick]);

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 10, 200));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 10, 50));
  };

  const handleRefresh = () => {
    setZoomLevel(100); // Reset zoom on refresh
    renderMermaid();
  };

  const handleDownloadPDF = () => {
    if (diagramRef.current) {
      const svgElement = diagramRef.current.querySelector('svg');
      if (svgElement) {
        // Create a canvas to convert SVG to image
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const svgData = new XMLSerializer().serializeToString(svgElement);

        // Create an image from the SVG
        const img = new Image();
        const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
        const url = URL.createObjectURL(svgBlob);

        img.onload = () => {
          // Set canvas size to match SVG
          canvas.width = img.width || 800;
          canvas.height = img.height || 600;

          // Fill white background
          if (ctx) {
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);

            // Convert canvas to PDF using jsPDF
            canvas.toBlob((blob) => {
              if (blob) {
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'mermaid-diagram.png';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(link.href);
              }
            }, 'image/png');
          }

          URL.revokeObjectURL(url);
        };

        img.src = url;
      }
    }
  };

  return (
    <div className="mermaid-container w-full h-full flex flex-col items-center">
      {/* Enhanced Top Bar Options */}
      <div className="flex items-center justify-end w-full p-3 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-b border-gray-200 dark:border-gray-600 rounded-t-lg">
        <div className="flex items-center space-x-1">
          <Button variant="ghost" size="sm" onClick={handleZoomOut} title="Zoom Out" className="hover:bg-white/50 dark:hover:bg-gray-600/50">
            <ZoomOut className="h-4 w-4" />
          </Button>
          <span className="text-sm font-medium mx-2 px-2 py-1 bg-white dark:bg-gray-600 rounded-md border">{zoomLevel}%</span>
          <Button variant="ghost" size="sm" onClick={handleZoomIn} title="Zoom In" className="hover:bg-white/50 dark:hover:bg-gray-600/50">
            <ZoomIn className="h-4 w-4" />
          </Button>
          <div className="w-px h-4 bg-gray-300 dark:bg-gray-500 mx-1"></div>
          <Button variant="ghost" size="sm" onClick={handleRefresh} title="Refresh" className="hover:bg-white/50 dark:hover:bg-gray-600/50">
            <RefreshCw className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={handleDownloadPDF} title="Download as Image" className="hover:bg-white/50 dark:hover:bg-gray-600/50">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div
        ref={diagramRef}
        className="mermaid flex justify-center items-center min-h-[400px] bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 rounded-b-lg p-6 shadow-inner overflow-auto w-full"
        style={{ flexGrow: 1 }} // Allow the diagram area to grow
      />
    </div>
  );
};

export default MermaidGraph;
