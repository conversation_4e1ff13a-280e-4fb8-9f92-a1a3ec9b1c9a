import React from 'react';
import { Calendar, MapPin, User, Building, Hash, Alert<PERSON>riangle, CheckCircle2, Clock, TrendingUp, FileText, BarChart, Search } from 'lucide-react';

interface RequestDetailsOverviewProps {
  request: any;
  indexEntries: any[];
  actesByIndex: { [key: string]: any[] };
}

const RequestDetailsOverview: React.FC<RequestDetailsOverviewProps> = ({
  request,
  indexEntries,
  actesByIndex
}) => {
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return 'Date invalide';
    }
  };

  const translateStatus = (status: string): string => {
    switch (status?.toLowerCase()) {
      case 'pending': return 'En attente';
      case 'inprogress':
      case 'in progress': return 'En cours';
      case 'completed': return 'Complé<PERSON>';
      case 'error': return 'Erreur';
      default: return status || 'Inconnu';
    }
  };

  return (
    <div className="space-y-6">
      {/* Enhanced Header with gradient background */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950 dark:to-indigo-900 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-4">
            <div className="h-16 w-16 rounded-xl bg-primary/10 flex items-center justify-center">
              <Building className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {request?.seller_name || 'Chargement...'}
              </h1>
              <div className="flex items-center space-x-2 mt-2">
                <MapPin className="h-4 w-4 text-gray-500" />
                <p className="text-gray-600 dark:text-gray-300">
                  {request?.seller_address || ''}
                </p>
              </div>
              <div className="flex items-center space-x-4 mt-3">
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Créée le {formatDate(request?.created_at)}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  <Hash className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-mono text-gray-600 dark:text-gray-400">
                    #{request?.id?.substring(0, 8)}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
              request?.status === 'completed'
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : request?.status === 'error'
                ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
            }`}>
              {request?.status === 'completed' && <CheckCircle2 className="h-4 w-4 mr-1" />}
              {request?.status === 'error' && <AlertTriangle className="h-4 w-4 mr-1" />}
              {!['completed', 'error'].includes(request?.status) && <Clock className="h-4 w-4 mr-1" />}
              {translateStatus(request?.status)}
            </div>
            {request?.completed_at && (
              <p className="text-xs text-gray-500 mt-2">
                Complétée le {formatDate(request?.completed_at)}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Enhanced Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Documents totaux</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white mt-1">
                {indexEntries.length + Object.values(actesByIndex).flat().length}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {indexEntries.length} index • {Object.values(actesByIndex).flat().length} actes
              </p>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-xl">
              <FileText className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Analyses</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white mt-1">
                {[request?.servitudes, request?.regimes_matrimoniaux, request?.erreurs, request?.autres_considerations].filter(Boolean).length}/4
              </p>
              <p className="text-xs text-gray-500 mt-1">sections complétées</p>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-xl">
              <TrendingUp className="h-8 w-8 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Servitudes</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white mt-1">
                {request?.servitudes ? '3' : '0'}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {request?.servitudes ? 'identifiées' : 'en analyse'}
              </p>
            </div>
            <div className="p-3 bg-orange-100 dark:bg-orange-900 rounded-xl">
              <AlertTriangle className="h-8 w-8 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Alertes</p>
              <p className="text-3xl font-bold text-gray-900 dark:text-white mt-1">
                {request?.erreurs ? '1' : '0'}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {request?.erreurs ? 'nécessite attention' : 'aucune erreur'}
              </p>
            </div>
            <div className="p-3 bg-red-100 dark:bg-red-900 rounded-xl">
              {request?.erreurs ? (
                <AlertTriangle className="h-8 w-8 text-red-600 dark:text-red-400" />
              ) : (
                <CheckCircle2 className="h-8 w-8 text-green-600 dark:text-green-400" />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Request Information Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Client Information */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center mb-4">
            <User className="h-5 w-5 text-primary mr-2" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Informations client</h3>
          </div>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Nom du vendeur</label>
              <p className="text-gray-900 dark:text-white font-medium">{request?.seller_name || 'Non spécifié'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Adresse de la propriété</label>
              <p className="text-gray-900 dark:text-white">{request?.seller_address || 'Non spécifiée'}</p>
            </div>
            {request?.property_details && (
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Détails de la propriété</label>
                <p className="text-gray-900 dark:text-white">{request.property_details}</p>
              </div>
            )}
          </div>
        </div>

        {/* Search Criteria */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center mb-4">
            <Search className="h-5 w-5 text-primary mr-2" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Critères de recherche</h3>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Inclure les actes radiés</span>
              <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                request?.inclure_actes_radies
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
              }`}>
                {request?.inclure_actes_radies ? 'Oui' : 'Non'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Ventes</span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {request?.sales_years ? `${request.sales_years} ${request.sales_years === 1 ? 'an' : 'ans'}` : 'Non spécifié'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Hypothèques</span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {request?.hypotheques_years ? `${request.hypotheques_years} ${request.hypotheques_years === 1 ? 'an' : 'ans'}` : 'Non spécifié'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Analysis Status */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center mb-6">
          <BarChart className="h-5 w-5 text-primary mr-2" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">État détaillé des analyses</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Servitudes</span>
              {request?.servitudes ? (
                <CheckCircle2 className="h-4 w-4 text-green-500" />
              ) : (
                <Clock className="h-4 w-4 text-yellow-500" />
              )}
            </div>
            <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
              request?.servitudes
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
            }`}>
              {request?.servitudes ? 'Complété' : 'À vérifier'}
            </span>
          </div>

          <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Régimes matrimoniaux</span>
              {request?.regimes_matrimoniaux ? (
                <CheckCircle2 className="h-4 w-4 text-green-500" />
              ) : (
                <Clock className="h-4 w-4 text-yellow-500" />
              )}
            </div>
            <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
              request?.regimes_matrimoniaux
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
            }`}>
              {request?.regimes_matrimoniaux ? 'Analysé' : 'En cours'}
            </span>
          </div>

          <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Erreurs identifiées</span>
              {request?.erreurs ? (
                <AlertTriangle className="h-4 w-4 text-red-500" />
              ) : (
                <CheckCircle2 className="h-4 w-4 text-green-500" />
              )}
            </div>
            <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
              request?.erreurs
                ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            }`}>
              {request?.erreurs ? 'Trouvées' : 'Aucune'}
            </span>
          </div>

          <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Autres considérations</span>
              {request?.autres_considerations ? (
                <CheckCircle2 className="h-4 w-4 text-green-500" />
              ) : (
                <Clock className="h-4 w-4 text-yellow-500" />
              )}
            </div>
            <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
              request?.autres_considerations
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
            }`}>
              {request?.autres_considerations ? 'Complété' : 'En cours'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestDetailsOverview;
