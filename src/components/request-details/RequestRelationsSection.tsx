import React from 'react';
import { Network } from 'lucide-react';
import { Button } from '@/components/ui/button';
import MermaidGraph from '@/components/ui/mermaid-graph';

interface RequestRelationsSectionProps {
  mermaidDefinition: string;
  onNodeClick: (nodeId: string, nodeType: 'request' | 'index' | 'acte' | 'radiated') => void;
}

const RequestRelationsSection: React.FC<RequestRelationsSectionProps> = ({
  mermaidDefinition,
  onNodeClick
}) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold flex items-center">
          <Network className="h-6 w-6 mr-2 text-primary" />
          Relations & Graphique
        </h2>
      </div>

      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg border border-dashed p-6">
        {mermaidDefinition ? (
          <MermaidGraph
            definition={mermaidDefinition}
            onNodeClick={onNodeClick}
          />
        ) : (
          <div className="text-center py-6">
            <Network className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
              Graphique Mermaid des Relations
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">
              Visualisation interactive des relations entre les documents, propriétés et personnes impliquées dans cette demande.
            </p>
            <div className="space-y-3">
              <Button variant="outline" disabled>
                <Network className="h-4 w-4 mr-2" />
                Générer le graphique
              </Button>
              <p className="text-xs text-gray-400">
                Fonctionnalité en cours de développement
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Legend Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border p-6 mt-6 shadow-sm">
        <h3 className="text-lg font-semibold mb-4">
          Légende du graphique
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
            <span className="w-4 h-4 rounded-lg bg-[#F59E0B] border-2 border-[#D97706]"></span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Demande</span>
          </div>
          <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
            <span className="w-4 h-4 rounded-lg bg-[#3B82F6] border-2 border-[#2563EB]"></span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">En cours</span>
          </div>
          <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
            <span className="w-4 h-4 rounded-lg bg-[#10B981] border-2 border-[#059669]"></span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Complété</span>
          </div>
          <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
            <span className="w-4 h-4 rounded-lg bg-[#EF4444] border-2 border-[#DC2626]"></span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Erreur</span>
          </div>
          <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700">
            <span className="w-4 h-4 border-2 border-dashed border-[#F87171] rounded-lg bg-[#F87171]/20"></span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Radié</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestRelationsSection;
