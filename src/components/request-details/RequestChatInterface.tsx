import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { <PERSON>, CardContent, <PERSON>Footer, CardHeader, CardTitle } from '@/components/ui/card';
import { useChatSession } from '@/hooks/chat/use-chat-session';
import { useChatMessages } from '@/hooks/chat/use-chat-messages';
import ChatMessageList from './chat/ChatMessageList';
import ChatInput from './chat/ChatInput';

interface RequestChatInterfaceProps {
  requestId: string;
}

const RequestChatInterface: React.FC<RequestChatInterfaceProps> = ({ requestId }) => {
  const { user } = useAuth();
  const [newMessage, setNewMessage] = useState('');

  // Use custom hooks for session and messages management
  const {
    chatSessionId,
    loadingSession,
    error: sessionError,
    findOrCreateChatSession,
    setError: setSessionError
  } = useChatSession(requestId);

  const {
    messages,
    loadingMessages,
    sendingMessage,
    isWaitingForResponse,
    hasUserSentMessage,
    error: messagesError,
    sendMessage,
    setError: setMessagesError
  } = useChatMessages(chatSessionId, requestId);

  // Combine errors from both hooks
  const error = sessionError || messagesError;

  // Handle sending messages
  const handleSendMessage = async (e?: React.FormEvent) => {
    e?.preventDefault();
    if (!newMessage.trim()) return;

    await sendMessage(newMessage);
    setNewMessage('');
  };

  // Initialize chat session on mount
  useEffect(() => {
    findOrCreateChatSession();
  }, [findOrCreateChatSession]);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Chat</CardTitle>
      </CardHeader>
      <CardContent className="pr-0 h-[400px]">
        {(loadingSession || loadingMessages) ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-center text-muted-foreground">
              {loadingSession ? 'Initialisation de la session de chat...' : 'Chargement des messages...'}
            </p>
          </div>
        ) : !hasUserSentMessage ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-center text-muted-foreground">Envoyez un message pour démarrer la conversation.</p>
          </div>
        ) : (
          <ChatMessageList
            messages={messages}
            user={user}
            isWaitingForResponse={isWaitingForResponse}
            error={error}
            loadingMessages={loadingMessages}
          />
        )}
      </CardContent>
      <CardFooter>
        <ChatInput
          newMessage={newMessage}
          setNewMessage={setNewMessage}
          onSendMessage={handleSendMessage}
          disabled={loadingSession || !!error}
          sendingMessage={sendingMessage}
        />
      </CardFooter>
    </Card>
  );
};

export default RequestChatInterface;
