import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface ChatInputProps {
  newMessage: string;
  setNewMessage: (message: string) => void;
  onSendMessage: (e?: React.FormEvent) => void;
  disabled: boolean;
  sendingMessage: boolean;
}

const ChatInput: React.FC<ChatInputProps> = ({
  newMessage,
  setNewMessage,
  onSendMessage,
  disabled,
  sendingMessage
}) => {
  return (
    <form onSubmit={onSendMessage} className="flex w-full items-center space-x-2">
      <Input
        id="message"
        placeholder="Écrivez votre message..."
        className="flex-1"
        autoComplete="off"
        value={newMessage}
        onChange={(event) => setNewMessage(event.target.value)}
        disabled={disabled}
      />
      <Button 
        type="submit" 
        size="icon" 
        disabled={!newMessage.trim() || disabled || sendingMessage}
      >
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="24" 
          height="24" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          className="h-4 w-4"
        >
          <path d="m22 2-7 20-4-9-9-4Z"/>
          <path d="m22 2-11 11"/>
        </svg>
        <span className="sr-only">Envoyer</span>
      </Button>
    </form>
  );
};

export default ChatInput;
