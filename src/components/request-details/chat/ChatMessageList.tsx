import React, { useRef, useEffect, useCallback } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import ChatMessage from './ChatMessage';
import ThinkingAnimation from '../ThinkingAnimation';

interface ChatMessage {
  id: string;
  chat_session_id: string;
  request_id: string;
  sender: 'user' | 'bot' | 'assistant';
  content: string;
  user_id?: string;
  created_at: string;
  metadata?: Record<string, any>;
}

interface ChatMessageListProps {
  messages: ChatMessage[];
  user?: any;
  isWaitingForResponse: boolean;
  error: string | null;
  loadingMessages: boolean;
}

const ChatMessageList: React.FC<ChatMessageListProps> = ({
  messages,
  user,
  isWaitingForResponse,
  error,
  loadingMessages
}) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messageEndRef = useRef<HTMLDivElement>(null);
  const initialScrollDone = useRef<boolean>(false);

  // Enhanced scroll to bottom function that works more reliably
  const scrollToBottom = useCallback(() => {
    // Use requestAnimationFrame to ensure DOM has been updated
    requestAnimationFrame(() => {
      if (messageEndRef.current) {
        messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
      } else if (scrollAreaRef.current) {
        scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
      }
    });
  }, []);

  // Call scroll to bottom whenever messages change
  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages, scrollToBottom]);

  // This effect will force scroll to bottom once messages are loaded and component is mounted
  useEffect(() => {
    // Only run on initial messages load
    if (messages.length > 0 && !initialScrollDone.current && !loadingMessages) {
      console.log("Executing initial scroll to bottom");
      initialScrollDone.current = true;
      
      // Use multiple attempts with increasing delays for better reliability
      const scrollAttempts = [0, 100, 300, 500];
      
      scrollAttempts.forEach(delay => {
        setTimeout(() => {
          if (messageEndRef.current) {
            messageEndRef.current.scrollIntoView({ behavior: delay === 0 ? 'auto' : 'smooth' });
          } else if (scrollAreaRef.current) {
            scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
          }
        }, delay);
      });
    }
  }, [messages, loadingMessages]);

  return (
    <ScrollArea className="h-full w-full pr-6" ref={scrollAreaRef}>
      {messages.length > 0 ? (
        messages.map((message) => (
          <ChatMessage
            key={message.id}
            message={message}
            user={user}
          />
        ))
      ) : null}
      {/* This invisible div serves as a marker for scrolling to the bottom */}
      <div ref={messageEndRef} style={{ height: '1px', width: '100%' }} />
      {isWaitingForResponse && (
        <div className="mt-4">
          <ThinkingAnimation />
        </div>
      )}
      {error && (
        <p className="text-center text-red-500 text-sm mt-4 px-6">
          Erreur: {error}
        </p>
      )}
    </ScrollArea>
  );
};

export default ChatMessageList;
