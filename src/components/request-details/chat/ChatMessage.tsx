import React from 'react';
import ReactMarkdown from 'react-markdown';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';

interface ChatMessage {
  id: string;
  chat_session_id: string;
  request_id: string;
  sender: 'user' | 'bot' | 'assistant';
  content: string;
  user_id?: string;
  created_at: string;
  metadata?: Record<string, any>;
}

interface ChatMessageProps {
  message: ChatMessage;
  user?: any;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message, user }) => {
  return (
    <div
      className={cn(
        'mb-4 flex items-end gap-2',
        message.sender === 'user' ? 'justify-end' : 'justify-start'
      )}
    >
      {message.sender !== 'user' && (
        <Avatar className="h-8 w-8">
          <AvatarFallback>B</AvatarFallback>
        </Avatar>
      )}
      <div
        className={cn(
          'max-w-[75%] rounded-lg p-3 text-sm',
          message.sender === 'user'
            ? 'bg-primary text-primary-foreground'
            : 'bg-muted'
        )}
      >
        {message.sender === 'user' ? (
          <p className="whitespace-pre-wrap">{message.content}</p>
        ) : (
          <ReactMarkdown
            components={{
              p: ({ node, ...props }) => (
                <p className="my-1 leading-normal" {...props} />
              ),
              h1: ({ node, ...props }) => (
                <h1 className="mt-2 mb-1 text-foreground text-xl font-bold" {...props} />
              ),
              h2: ({ node, ...props }) => (
                <h2 className="mt-2 mb-1 text-foreground text-lg font-bold" {...props} />
              ),
              h3: ({ node, ...props }) => (
                <h3 className="mt-2 mb-1 text-foreground text-md font-bold" {...props} />
              ),
              code: ({ ...props }) => {
                const isInline = !props.className;
                return isInline ? (
                  <code className="bg-muted-foreground/20 p-1 rounded" {...props} />
                ) : (
                  <code className="bg-muted-foreground/20 p-1 rounded" {...props} />
                );
              },
              pre: ({ node, ...props }) => (
                <pre className="bg-muted-foreground/20 p-2 rounded overflow-x-auto my-2" {...props} />
              ),
              ul: ({ node, ...props }) => (
                <ul className="list-disc pl-6 my-2" {...props} />
              ),
              ol: ({ node, ...props }) => (
                <ol className="list-decimal pl-6 my-2" {...props} />
              ),
              li: ({ node, ...props }) => (
                <li className="my-1" {...props} />
              ),
              a: ({ node, ...props }) => (
                <a className="text-blue-500 hover:underline" {...props} />
              ),
              blockquote: ({ node, ...props }) => (
                <blockquote className="border-l-4 border-muted-foreground/50 pl-4 italic my-2" {...props} />
              ),
            }}
          >
            {message.content}
          </ReactMarkdown>
        )}
        <p className={cn(
          "mt-1 text-xs",
          message.sender === 'user' ? 'text-primary-foreground/80 text-right' : 'text-muted-foreground text-left'
        )}>
          {formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}
        </p>
      </div>
      {message.sender === 'user' && user && (
        <Avatar className="h-8 w-8">
          <AvatarImage src={user.user_metadata?.avatar_url} alt={user.email} />
          <AvatarFallback>{user.email?.[0].toUpperCase()}</AvatarFallback>
        </Avatar>
      )}
    </div>
  );
};

export default ChatMessage;
