import React from 'react';
import { <PERSON><PERSON><PERSON>, FileText, Star, Search, FolderOpen, Network } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import DocumentsList from './DocumentsList';

interface SidebarSection {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  completed: boolean;
}

interface RequestDetailsSidebarProps {
  selectedSection: string;
  onSectionChange: (sectionId: string) => void;
  request: any;
  indexEntries: any[];
  actesByIndex: { [key: string]: any[] };
  selectedPdfUrl?: string | null;
  onPdfSelect?: (url: string) => void;
}

const RequestDetailsSidebar: React.FC<RequestDetailsSidebarProps> = ({
  selectedSection,
  onSectionChange,
  request,
  indexEntries,
  actesByIndex,
  selectedPdfUrl,
  onPdfSelect
}) => {
  const sidebarSections: SidebarSection[] = [
    { id: 'overview', label: 'Vue d\'ensemble', icon: <PERSON><PERSON>hart, completed: true },
    { id: 'research-summary', label: 'Résumé de la recherche', icon: FileText, completed: !!request.research_summary },
    { id: 'highlights', label: 'Faits saillants', icon: Star, completed: !!(request.servitudes || request.regimes_matrimoniaux || request.erreurs || request.autres_considerations) },
    { id: 'research-steps', label: 'Étapes de recherches', icon: Search, completed: indexEntries.length > 0 },
    { id: 'documents', label: 'Documents', icon: FolderOpen, completed: indexEntries.length > 0 || Object.values(actesByIndex).flat().length > 0 },
    { id: 'relations', label: 'Relations', icon: Network, completed: false }
  ];

  return (
    <div className="w-72 sticky top-20 h-fit space-y-4">
      <Card>
        <CardContent className="p-3">
          <h3 className="font-semibold mb-3 text-xs uppercase tracking-wide text-muted-foreground">
            Analyses
          </h3>
          <div className="space-y-1">
            {sidebarSections.map((section) => {
              const IconComponent = section.icon;
              return (
                <button
                  key={section.id}
                  onClick={() => onSectionChange(section.id)}
                  className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                    selectedSection === section.id
                      ? 'bg-primary/10 text-primary border border-primary/20'
                      : 'hover:bg-muted text-muted-foreground hover:text-foreground'
                  }`}
                >
                  <IconComponent className="h-4 w-4" />
                  <div className="flex-1">
                    <span className="text-sm font-medium">{section.label}</span>
                  </div>
                </button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Documents List - Only show when documents section is selected */}
      {selectedSection === 'documents' && (
        <Card>
          <CardContent className="p-3">
            <DocumentsList
              indexEntries={indexEntries}
              actesByIndex={actesByIndex}
              selectedPdfUrl={selectedPdfUrl || null}
              onPdfSelect={onPdfSelect || (() => {})}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default RequestDetailsSidebar;
