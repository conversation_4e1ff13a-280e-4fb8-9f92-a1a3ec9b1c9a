import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import PdfViewer from '@/components/ui/pdf-viewer';
import { type DocumentInfo } from '@/lib/document-utils';

interface Document extends DocumentInfo {
  source?: any;
}

interface RequestDocumentsSectionProps {
  indexEntries: any[];
  actesByIndex: { [key: string]: any[] };
  selectedPdfUrl: string | null;
  onPdfSelect: (url: string) => void;
}

const RequestDocumentsSection: React.FC<RequestDocumentsSectionProps> = ({
  indexEntries,
  actesByIndex,
  selectedPdfUrl,
  onPdfSelect
}) => {
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);

  // Update selected document when PDF URL changes
  React.useEffect(() => {
    if (selectedPdfUrl) {
      // Find the document that matches the selected PDF URL
      const allDocuments: Document[] = [
        ...indexEntries
          .filter(index => index.doc_url)
          .map(index => ({
            id: index.id,
            name: index.doc_number ? `Index ${index.doc_number}` : `Index ${index.id.slice(0, 8)}`,
            url: index.doc_url,
            type: 'index' as const,
            source: index
          })),
        ...Object.values(actesByIndex)
          .flat()
          .filter(acte => acte.doc_url)
          .map(acte => ({
            id: acte.id,
            name: acte.acte_publication_number ? `Acte ${acte.acte_publication_number}` : `Acte ${acte.id.slice(0, 8)}`,
            url: acte.doc_url,
            type: 'acte' as const,
            source: acte
          }))
      ];

      const matchingDocument = allDocuments.find(doc => doc.url === selectedPdfUrl);
      if (matchingDocument) {
        setSelectedDocument(matchingDocument);
      }
    } else {
      setSelectedDocument(null);
    }
  }, [selectedPdfUrl, indexEntries, actesByIndex]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold flex items-center">
          <Eye className="h-6 w-6 mr-2 text-primary" />
          Visionneuse de documents
        </h2>
      </div>

      {/* PDF Viewer - Full Width */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border">
        <div className="p-4 border-b">
          <h3 className="text-lg font-semibold flex items-center">
            <Eye className="h-5 w-5 mr-2 text-primary" />
            Document
            {selectedDocument && (
              <span className="ml-2 text-sm font-normal text-gray-500 truncate">
                - {selectedDocument.name}
              </span>
            )}
          </h3>
        </div>
        <div className="p-4">
          {selectedPdfUrl && selectedDocument ? (
            <div className="h-[600px]">
              <PdfViewer
                fileUrl={selectedPdfUrl}
                fileName={selectedDocument.name}
              />
            </div>
          ) : (
            <div className="text-center py-16">
              <FolderOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h4 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                Visionneuse de documents
              </h4>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                Sélectionnez un document dans la liste de gauche pour le visualiser.
              </p>
              <Button variant="outline" disabled>
                <Eye className="h-4 w-4 mr-2" />
                Aucun document sélectionné
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RequestDocumentsSection;
