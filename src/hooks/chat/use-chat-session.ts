import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';

export const useChatSession = (requestId: string) => {
  const { user } = useAuth();
  const [chatSessionId, setChatSessionId] = useState<string | null>(null);
  const [loadingSession, setLoadingSession] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const findOrCreateChatSession = useCallback(async () => {
    if (!requestId) return;
    setLoadingSession(true);
    setError(null);
    console.log(`Looking for chat session for request ID: ${requestId}`);

    try {
      const { data: existingSession, error: findError } = await supabase
        .from('chat_sessions')
        .select('id')
        .eq('metadata->>related_request_id', requestId)
        .maybeSingle();

      if (findError) {
        console.error('Error finding chat session:', findError);
        throw new Error(`Failed to find chat session: ${findError.message}`);
      }

      if (existingSession) {
        console.log(`Found existing chat session: ${existingSession.id}`);
        setChatSessionId(existingSession.id);
      } else {
        if (!user) {
          console.error("User not authenticated. Cannot create chat session.");
          throw new Error("User not authenticated. Cannot create chat session.");
        }
        const { data: newSession, error: createError } = await supabase
          .from('chat_sessions')
          .insert({
            metadata: { related_request_id: requestId },
            user_id: user.id
          })
          .select('id')
          .single();

        if (createError) {
          console.error('Error creating chat session:', createError);
          throw new Error(`Failed to create chat session: ${createError.message}`);
        }

        if (newSession) {
          console.log(`Created new chat session: ${newSession.id}`);
          setChatSessionId(newSession.id);
        } else {
          throw new Error('Failed to create chat session, no ID returned.');
        }
      }
    } catch (err: any) {
      console.error('Error in findOrCreateChatSession:', err);
      setError(err.message || 'An unexpected error occurred while setting up the chat session.');
    } finally {
      setLoadingSession(false);
    }
  }, [requestId, user]);

  return {
    chatSessionId,
    loadingSession,
    error,
    findOrCreateChatSession,
    setError
  };
};
