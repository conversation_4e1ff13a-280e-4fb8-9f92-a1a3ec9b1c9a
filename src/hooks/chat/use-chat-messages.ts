import { useState, useCallback, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';

interface ChatMessage {
  id: string;
  chat_session_id: string;
  request_id: string;
  sender: 'user' | 'bot' | 'assistant';
  content: string;
  user_id?: string;
  created_at: string;
  metadata?: Record<string, any>;
}

export const useChatMessages = (chatSessionId: string | null, requestId: string) => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [isWaitingForResponse, setIsWaitingForResponse] = useState(false);
  const [hasUserSentMessage, setHasUserSentMessage] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMessages = useCallback(async () => {
    if (!chatSessionId) return;
    setLoadingMessages(true);
    setError(null);
    console.log(`Fetching messages for session ID: ${chatSessionId}`);

    try {
      const { data, error: fetchError } = await supabase
        .from('messages')
        .select('id, chat_session_id, request_id, sender, content, user_id, created_at, metadata')
        .eq('chat_session_id', chatSessionId)
        .order('created_at', { ascending: true });

      if (fetchError) {
        console.error('Error fetching messages:', fetchError);
        throw new Error(`Failed to fetch messages: ${fetchError.message}`);
      }

      console.log(`Fetched ${data?.length ?? 0} messages.`);
      setMessages((data as ChatMessage[]) || []);
    } catch (err: any) {
      console.error('Error in fetchMessages:', err);
      setError(err.message || 'An unexpected error occurred while fetching messages.');
    } finally {
      setLoadingMessages(false);
    }
  }, [chatSessionId]);

  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || !chatSessionId || !user || sendingMessage) return;

    setSendingMessage(true);
    setIsWaitingForResponse(true);
    setError(null);
    const traceId = crypto.randomUUID();
    console.log(`Sending message for session ${chatSessionId}, request ${requestId}, trace ${traceId}`);

    try {
      const { error: insertError } = await supabase
        .from('messages')
        .insert({
          chat_session_id: chatSessionId,
          request_id: requestId,
          sender: 'user',
          content: content.trim(),
          user_id: user.id,
          metadata: { trace_id: traceId }
        });

      if (insertError) {
        console.error('Error sending message:', insertError);
        throw new Error(`Failed to send message: ${insertError.message}`);
      }

      console.log('Message sent successfully.');
    } catch (err: any) {
      console.error('Error in sendMessage:', err);
      setError(err.message || 'An unexpected error occurred while sending the message.');
    } finally {
      setSendingMessage(false);
    }
  }, [chatSessionId, requestId, user, sendingMessage]);

  // Set up real-time subscription
  useEffect(() => {
    if (!chatSessionId) return;

    fetchMessages();

    console.log(`Setting up real-time subscription for session: ${chatSessionId}`);
    const channel = supabase.channel(`chat-session-${chatSessionId}`)
      .on<ChatMessage>(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `chat_session_id=eq.${chatSessionId}`
        },
        (payload) => {
          console.log('Real-time: New message received:', payload.new);
          const newMessagePayload = payload.new as ChatMessage;
          setMessages((currentMessages) => {
            if (currentMessages.some(msg => msg.id === newMessagePayload.id)) {
              return currentMessages;
            }
            return [...currentMessages, newMessagePayload];
          });
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log(`Successfully subscribed to channel chat-session-${chatSessionId}`);
        } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
          console.error(`Subscription error on channel chat-session-${chatSessionId}:`, err);
          setError(`Real-time connection error: ${err?.message || 'Failed to subscribe'}`);
        } else if (status === 'CLOSED') {
          console.log(`Subscription closed for channel chat-session-${chatSessionId}`);
        }
      });

    return () => {
      console.log(`Unsubscribing from channel chat-session-${chatSessionId}`);
      supabase.removeChannel(channel).catch(err => {
        console.error("Error removing channel:", err);
      });
    };
  }, [chatSessionId, fetchMessages]);

  // Track if user has sent a message
  useEffect(() => {
    if (!hasUserSentMessage && messages.some(msg => msg.sender === 'user')) {
      setHasUserSentMessage(true);
    }
  }, [messages, hasUserSentMessage]);

  return {
    messages,
    loadingMessages,
    sendingMessage,
    isWaitingForResponse,
    hasUserSentMessage,
    error,
    sendMessage,
    setError
  };
};
