import { describe, it, expect } from 'vitest';
import { processGoogleDriveUrl, getGoogleDriveDownloadUrl } from '../document-utils';

describe('Document Utils', () => {
  describe('processGoogleDriveUrl', () => {
    it('should convert Google Drive file URL to preview URL', () => {
      const input = 'https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view';
      const expected = 'https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/preview';
      expect(processGoogleDriveUrl(input)).toBe(expected);
    });

    it('should convert Google Drive open URL to preview URL', () => {
      const input = 'https://drive.google.com/open?id=1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms';
      const expected = 'https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/preview';
      expect(processGoogleDriveUrl(input)).toBe(expected);
    });

    it('should return non-Google Drive URLs unchanged', () => {
      const input = 'https://example.com/document.pdf';
      expect(processGoogleDriveUrl(input)).toBe(input);
    });

    it('should handle empty URLs', () => {
      expect(processGoogleDriveUrl('')).toBe('');
    });
  });

  describe('getGoogleDriveDownloadUrl', () => {
    it('should convert Google Drive file URL to download URL', () => {
      const input = 'https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view';
      const expected = 'https://drive.google.com/uc?export=download&id=1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms';
      expect(getGoogleDriveDownloadUrl(input)).toBe(expected);
    });

    it('should convert Google Drive open URL to download URL', () => {
      const input = 'https://drive.google.com/open?id=1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms';
      const expected = 'https://drive.google.com/uc?export=download&id=1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms';
      expect(getGoogleDriveDownloadUrl(input)).toBe(expected);
    });

    it('should return non-Google Drive URLs unchanged', () => {
      const input = 'https://example.com/document.pdf';
      expect(getGoogleDriveDownloadUrl(input)).toBe(input);
    });

    it('should handle empty URLs', () => {
      expect(getGoogleDriveDownloadUrl('')).toBe('');
    });
  });
});
